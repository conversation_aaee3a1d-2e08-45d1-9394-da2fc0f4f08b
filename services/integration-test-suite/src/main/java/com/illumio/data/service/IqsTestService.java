package com.illumio.data.service;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.illumio.data.configuration.IntegrationTestSuiteConfig;
import com.illumio.data.model.IqsRequest;
import com.illumio.data.model.TestResult;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Service;
import org.springframework.web.reactive.function.client.WebClient;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;
import reactor.util.retry.Retry;

import java.time.Instant;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Service for testing IQS (Insights Query Service) APIs
 */
@Service
public class IqsTestService {

    private static final Logger log = LoggerFactory.getLogger(IqsTestService.class);

    private final IntegrationTestSuiteConfig config;
    private final WebClient.Builder webClientBuilder;
    private final ObjectMapper objectMapper;

    public IqsTestService(IntegrationTestSuiteConfig config,
                         WebClient.Builder webClientBuilder,
                         ObjectMapper objectMapper) {
        this.config = config;
        this.webClientBuilder = webClientBuilder;
        this.objectMapper = objectMapper;
    }

    /**
     * Tests IQS service by first discovering available endpoints, then testing them
     */
    public Mono<List<TestResult>> runIqsTests(String tenantId) {
        log.info("Starting IQS integration tests for tenant: {}", tenantId);

        WebClient webClient = createWebClient();

        if (config.getTestConfig().isEnableWidgetDiscovery()) {
            return discoverAvailableWidgets(webClient, tenantId)
                    .flatMap(availableWidgets -> {
                        if (availableWidgets.isEmpty()) {
                            log.warn("No widgets discovered for tenant: {}, falling back to configured widget IDs", tenantId);
                            return testConfiguredWidgets(webClient, tenantId);
                        } else {
                            log.info("Discovered {} widgets for tenant: {}", availableWidgets.size(), tenantId);
                            return testDiscoveredWidgets(webClient, tenantId, availableWidgets);
                        }
                    })
                    .onErrorResume(error -> {
                        log.error("Failed to discover widgets for tenant: {}, falling back to configured widgets", tenantId, error);
                        return testConfiguredWidgets(webClient, tenantId);
                    });
        } else {
            log.info("Widget discovery disabled, using configured widget IDs for tenant: {}", tenantId);
            return testConfiguredWidgets(webClient, tenantId);
        }
    }

    /**
     * Discovers available widgets by querying the IQS service
     */
    private Mono<List<String>> discoverAvailableWidgets(WebClient webClient, String tenantId) {
        String pageId = config.getTestConfig().getPageId();

        // Try to discover widgets - this endpoint structure may need adjustment based on actual IQS API
        return webClient.get()
                .uri("/api/v1/tenant/{tenantId}/insights/{pageId}/widgets", tenantId, pageId)
                .retrieve()
                .bodyToMono(JsonNode.class)
                .map(this::extractWidgetIds)
                .doOnNext(widgets -> log.info("Discovered widget IDs: {}", widgets))
                .onErrorResume(error -> {
                    log.warn("Failed to discover widgets via GET endpoint, trying alternative discovery methods", error);
                    return tryAlternativeDiscovery(webClient, tenantId, pageId);
                });
    }

    /**
     * Try alternative methods to discover available widgets
     */
    private Mono<List<String>> tryAlternativeDiscovery(WebClient webClient, String tenantId, String pageId) {
        // Alternative 1: Try a metadata or configuration endpoint
        return webClient.get()
                .uri("/api/v1/tenant/{tenantId}/insights/{pageId}/metadata", tenantId, pageId)
                .retrieve()
                .bodyToMono(JsonNode.class)
                .map(this::extractWidgetIds)
                .onErrorResume(error -> {
                    log.warn("Alternative discovery also failed, will use configured widget IDs", error);
                    return Mono.just(List.of()); // Return empty list to trigger fallback
                });
    }

    /**
     * Tests widgets that were discovered dynamically
     */
    private Mono<List<TestResult>> testDiscoveredWidgets(WebClient webClient, String tenantId, List<String> widgetIds) {
        String pageId = config.getTestConfig().getPageId();

        return Flux.fromIterable(widgetIds)
                .flatMap(widgetId -> testWidgetEndpoint(webClient, tenantId, pageId, widgetId))
                .collectList();
    }

    /**
     * Tests widgets from configuration (fallback method)
     */
    private Mono<List<TestResult>> testConfiguredWidgets(WebClient webClient, String tenantId) {
        String pageId = config.getTestConfig().getPageId();

        return Flux.fromIterable(config.getTestConfig().getTestWidgetIds())
                .flatMap(widgetId -> testWidgetEndpoint(webClient, tenantId, pageId, widgetId))
                .collectList();
    }

    /**
     * Test widget endpoint using the correct IQS endpoint structure
     * /api/v1/tenant/{tenantId}/insights/{pageId}/widget/{widgetId}
     */
    private Mono<TestResult> testWidgetEndpoint(WebClient webClient, String tenantId, String pageId, String widgetId) {
        long startTime = System.currentTimeMillis();
        String testName = String.format("IQS Widget %s Query", widgetId);

        IqsRequest request = createWidgetRequest(tenantId, pageId, widgetId);
        String uri = String.format("/api/v1/tenant/%s/insights/%s/widget/%s", tenantId, pageId, widgetId);

        return webClient.post()
                .uri(uri)
                .contentType(MediaType.APPLICATION_JSON)
                .bodyValue(request)
                .retrieve()
                .bodyToMono(JsonNode.class)
                .map(response -> {
                    long executionTime = System.currentTimeMillis() - startTime;
                    return validateWidgetResponse(response, testName, tenantId, executionTime);
                })
                .retryWhen(Retry.backoff(config.getIqsConfig().getMaxRetries(),
                        config.getIqsConfig().getRetryDelay()))
                .onErrorReturn(createErrorResult(testName, tenantId, startTime));
    }

    private WebClient createWebClient() {
        WebClient.Builder builder = webClientBuilder
                .baseUrl(config.getIqsConfig().getBaseUrl())
                .defaultHeader(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_JSON_VALUE);

        if (config.getIqsConfig().getAuthKey() != null) {
            builder.defaultHeader("x-auth-key", config.getIqsConfig().getAuthKey());
        }

        return builder.build();
    }

    private IqsRequest createWidgetRequest(String tenantId, String pageId, String widgetId) {
        Instant endTime = Instant.now();
        Instant startTime = endTime.minus(config.getTestConfig().getTestTimeRange());

        return IqsRequest.builder()
                .currentTimeFrame(IqsRequest.TimeFrame.builder()
                        .startTime(startTime)
                        .endTime(endTime)
                        .build())
                .comparisonTimeFrame(IqsRequest.TimeFrame.builder()
                        .startTime(startTime.minus(config.getTestConfig().getComparisonTimeRange()))
                        .endTime(startTime)
                        .build())
                .filters(List.of())  // Tenant is already in the URL path
                .pagination(IqsRequest.Pagination.builder()
                        .page(0)
                        .size(100)
                        .build())
                .build();
    }

    private TestResult validateFlowResponse(JsonNode response, String testName, String tenantId, long executionTime) {
        List<TestResult.ValidationResult> validations = new ArrayList<>();
        Map<String, Object> metrics = new HashMap<>();
        
        // Extract metrics from response
        long flowCount = extractFlowCount(response);
        long byteCount = extractByteCount(response);
        
        metrics.put("flowCount", flowCount);
        metrics.put("byteCount", byteCount);
        metrics.put("responseSize", response.toString().length());
        
        // Validate flow count
        boolean flowCountValid = flowCount >= config.getTestConfig().getMinFlowCount();
        validations.add(TestResult.ValidationResult.builder()
                .validationType("flowCount")
                .passed(flowCountValid)
                .expectedValue(">= " + config.getTestConfig().getMinFlowCount())
                .actualValue(String.valueOf(flowCount))
                .message(flowCountValid ? "Flow count validation passed" : "Flow count below minimum threshold")
                .build());
        
        // Validate byte count
        boolean byteCountValid = byteCount >= config.getTestConfig().getMinByteCount();
        validations.add(TestResult.ValidationResult.builder()
                .validationType("byteCount")
                .passed(byteCountValid)
                .expectedValue(">= " + config.getTestConfig().getMinByteCount())
                .actualValue(String.valueOf(byteCount))
                .message(byteCountValid ? "Byte count validation passed" : "Byte count below minimum threshold")
                .build());
        
        // Determine overall test status
        boolean allValidationsPassed = validations.stream().allMatch(TestResult.ValidationResult::isPassed);
        TestResult.TestStatus status = allValidationsPassed ? TestResult.TestStatus.PASSED : TestResult.TestStatus.FAILED;
        
        return TestResult.builder()
                .testName(testName)
                .testType("IQS")
                .status(status)
                .tenantId(tenantId)
                .timestamp(Instant.now())
                .executionTime(TestResult.Duration.ofMillis(executionTime))
                .metrics(metrics)
                .validations(validations)
                .build();
    }

    private TestResult validateWidgetResponse(JsonNode response, String testName, String tenantId, long executionTime) {
        // Use the same validation logic as flow response
        return validateFlowResponse(response, testName, tenantId, executionTime);
    }

    /**
     * Extracts widget IDs from the discovery response
     */
    private List<String> extractWidgetIds(JsonNode response) {
        List<String> widgetIds = new ArrayList<>();

        try {
            // Try different possible response structures
            if (response.has("widgets") && response.get("widgets").isArray()) {
                for (JsonNode widget : response.get("widgets")) {
                    if (widget.has("id")) {
                        widgetIds.add(widget.get("id").asText());
                    } else if (widget.has("widgetId")) {
                        widgetIds.add(widget.get("widgetId").asText());
                    }
                }
            } else if (response.has("data") && response.get("data").has("widgets")) {
                JsonNode widgets = response.get("data").get("widgets");
                if (widgets.isArray()) {
                    for (JsonNode widget : widgets) {
                        if (widget.has("id")) {
                            widgetIds.add(widget.get("id").asText());
                        }
                    }
                }
            } else if (response.isArray()) {
                // Response might be directly an array of widget objects
                for (JsonNode widget : response) {
                    if (widget.has("id")) {
                        widgetIds.add(widget.get("id").asText());
                    } else if (widget.isTextual()) {
                        widgetIds.add(widget.asText());
                    }
                }
            }

            log.debug("Extracted {} widget IDs from discovery response", widgetIds.size());

        } catch (Exception e) {
            log.warn("Failed to extract widget IDs from response", e);
        }

        return widgetIds;
    }

    private long extractFlowCount(JsonNode response) {
        // Extract flow count from response - adjust path based on actual response structure
        if (response.has("data") && response.get("data").has("totalFlows")) {
            return response.get("data").get("totalFlows").asLong();
        }
        if (response.has("totalCount")) {
            return response.get("totalCount").asLong();
        }
        if (response.has("flows") && response.get("flows").isArray()) {
            return response.get("flows").size();
        }
        return 0;
    }

    private long extractByteCount(JsonNode response) {
        // Extract byte count from response - adjust path based on actual response structure
        if (response.has("data") && response.get("data").has("totalBytes")) {
            return response.get("data").get("totalBytes").asLong();
        }
        if (response.has("totalBytes")) {
            return response.get("totalBytes").asLong();
        }
        // Sum up bytes from individual flows if available
        if (response.has("flows") && response.get("flows").isArray()) {
            long totalBytes = 0;
            for (JsonNode flow : response.get("flows")) {
                if (flow.has("sentBytes")) {
                    totalBytes += flow.get("sentBytes").asLong();
                }
                if (flow.has("receivedBytes")) {
                    totalBytes += flow.get("receivedBytes").asLong();
                }
            }
            return totalBytes;
        }
        return 0;
    }

    private TestResult createErrorResult(String testName, String tenantId, long startTime) {
        long executionTime = System.currentTimeMillis() - startTime;
        return TestResult.builder()
                .testName(testName)
                .testType("IQS")
                .status(TestResult.TestStatus.ERROR)
                .tenantId(tenantId)
                .timestamp(Instant.now())
                .executionTime(TestResult.Duration.ofMillis(executionTime))
                .errorMessage("API call failed")
                .build();
    }
}
