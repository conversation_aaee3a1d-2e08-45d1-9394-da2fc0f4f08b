package com.illumio.data.model;

import java.time.Instant;
import java.util.List;
import java.util.Map;

/**
 * Represents the result of an integration test
 */
public class TestResult {
    private String testName;
    private String testType;  // IQS, ISS, HEALTH_CHECK
    private TestStatus status;
    private String tenantId;
    private Instant timestamp;
    private Duration executionTime;
    private String errorMessage;
    private Map<String, Object> metrics;
    private List<ValidationResult> validations;

    // Constructors
    public TestResult() {}

    public TestResult(String testName, String testType, TestStatus status, String tenantId,
                     Instant timestamp, Duration executionTime, String errorMessage,
                     Map<String, Object> metrics, List<ValidationResult> validations) {
        this.testName = testName;
        this.testType = testType;
        this.status = status;
        this.tenantId = tenantId;
        this.timestamp = timestamp;
        this.executionTime = executionTime;
        this.errorMessage = errorMessage;
        this.metrics = metrics;
        this.validations = validations;
    }

    // Getters and Setters
    public String getTestName() { return testName; }
    public void setTestName(String testName) { this.testName = testName; }

    public String getTestType() { return testType; }
    public void setTestType(String testType) { this.testType = testType; }

    public TestStatus getStatus() { return status; }
    public void setStatus(TestStatus status) { this.status = status; }

    public String getTenantId() { return tenantId; }
    public void setTenantId(String tenantId) { this.tenantId = tenantId; }

    public Instant getTimestamp() { return timestamp; }
    public void setTimestamp(Instant timestamp) { this.timestamp = timestamp; }

    public Duration getExecutionTime() { return executionTime; }
    public void setExecutionTime(Duration executionTime) { this.executionTime = executionTime; }

    public String getErrorMessage() { return errorMessage; }
    public void setErrorMessage(String errorMessage) { this.errorMessage = errorMessage; }

    public Map<String, Object> getMetrics() { return metrics; }
    public void setMetrics(Map<String, Object> metrics) { this.metrics = metrics; }

    public List<ValidationResult> getValidations() { return validations; }
    public void setValidations(List<ValidationResult> validations) { this.validations = validations; }

    // Builder pattern
    public static Builder builder() {
        return new Builder();
    }

    public static class Builder {
        private String testName;
        private String testType;
        private TestStatus status;
        private String tenantId;
        private Instant timestamp;
        private Duration executionTime;
        private String errorMessage;
        private Map<String, Object> metrics;
        private List<ValidationResult> validations;

        public Builder testName(String testName) { this.testName = testName; return this; }
        public Builder testType(String testType) { this.testType = testType; return this; }
        public Builder status(TestStatus status) { this.status = status; return this; }
        public Builder tenantId(String tenantId) { this.tenantId = tenantId; return this; }
        public Builder timestamp(Instant timestamp) { this.timestamp = timestamp; return this; }
        public Builder executionTime(Duration executionTime) { this.executionTime = executionTime; return this; }
        public Builder errorMessage(String errorMessage) { this.errorMessage = errorMessage; return this; }
        public Builder metrics(Map<String, Object> metrics) { this.metrics = metrics; return this; }
        public Builder validations(List<ValidationResult> validations) { this.validations = validations; return this; }

        public TestResult build() {
            return new TestResult(testName, testType, status, tenantId, timestamp,
                                executionTime, errorMessage, metrics, validations);
        }
    }

    public enum TestStatus {
        PASSED, FAILED, SKIPPED, ERROR
    }

    public static class ValidationResult {
        private String validationType;
        private boolean passed;
        private String expectedValue;
        private String actualValue;
        private String message;

        // Constructors
        public ValidationResult() {}

        public ValidationResult(String validationType, boolean passed, String expectedValue,
                              String actualValue, String message) {
            this.validationType = validationType;
            this.passed = passed;
            this.expectedValue = expectedValue;
            this.actualValue = actualValue;
            this.message = message;
        }

        // Getters and Setters
        public String getValidationType() { return validationType; }
        public void setValidationType(String validationType) { this.validationType = validationType; }

        public boolean isPassed() { return passed; }
        public void setPassed(boolean passed) { this.passed = passed; }

        public String getExpectedValue() { return expectedValue; }
        public void setExpectedValue(String expectedValue) { this.expectedValue = expectedValue; }

        public String getActualValue() { return actualValue; }
        public void setActualValue(String actualValue) { this.actualValue = actualValue; }

        public String getMessage() { return message; }
        public void setMessage(String message) { this.message = message; }

        // Builder pattern
        public static Builder builder() {
            return new Builder();
        }

        public static class Builder {
            private String validationType;
            private boolean passed;
            private String expectedValue;
            private String actualValue;
            private String message;

            public Builder validationType(String validationType) { this.validationType = validationType; return this; }
            public Builder passed(boolean passed) { this.passed = passed; return this; }
            public Builder expectedValue(String expectedValue) { this.expectedValue = expectedValue; return this; }
            public Builder actualValue(String actualValue) { this.actualValue = actualValue; return this; }
            public Builder message(String message) { this.message = message; return this; }

            public ValidationResult build() {
                return new ValidationResult(validationType, passed, expectedValue, actualValue, message);
            }
        }
    }

    public static class Duration {
        private long milliseconds;

        // Constructors
        public Duration() {}

        public Duration(long milliseconds) {
            this.milliseconds = milliseconds;
        }

        // Getters and Setters
        public long getMilliseconds() { return milliseconds; }
        public void setMilliseconds(long milliseconds) { this.milliseconds = milliseconds; }

        public static Duration ofMillis(long millis) {
            return new Duration(millis);
        }

        // Builder pattern
        public static Builder builder() {
            return new Builder();
        }

        public static class Builder {
            private long milliseconds;

            public Builder milliseconds(long milliseconds) { this.milliseconds = milliseconds; return this; }

            public Duration build() {
                return new Duration(milliseconds);
            }
        }

        @Override
        public String toString() {
            return milliseconds + "ms";
        }
    }
}
