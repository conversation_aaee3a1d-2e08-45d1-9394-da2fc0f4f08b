package com.illumio.data.service;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.illumio.data.configuration.IntegrationTestSuiteConfig;
import com.illumio.data.model.IssRequest;
import com.illumio.data.model.TestResult;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Service;
import org.springframework.web.reactive.function.client.WebClient;

import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;
import reactor.util.retry.Retry;

import java.time.Instant;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Service for testing ISS (Insights Search Service) APIs
 */
@Service
public class IssTestService {

    private static final Logger log = LoggerFactory.getLogger(IssTestService.class);

    private final IntegrationTestSuiteConfig config;
    private final WebClient.Builder webClientBuilder;
    private final ObjectMapper objectMapper;

    public IssTestService(IntegrationTestSuiteConfig config,
                         WebClient.Builder webClientBuilder,
                         ObjectMapper objectMapper) {
        this.config = config;
        this.webClientBuilder = webClientBuilder;
        this.objectMapper = objectMapper;
    }

    /**
     * Tests ISS service with various search scenarios
     */
    public Mono<List<TestResult>> runIssTests(String tenantId) {
        log.info("Starting ISS integration tests for tenant: {}", tenantId);
    
        WebClient webClient = createWebClient();
    
        // Compose all test Monos into a Flux and collect as a list
        return Flux.concat(
                testBasicSearch(webClient, tenantId)
                // add more tests here if needed, e.g. testIpAddressSearch(...)
        ).collectList();
    }

    /**
     * Test basic search functionality
     */
    private Mono<TestResult> testBasicSearch(WebClient webClient, String tenantId) {
        long startTime = System.currentTimeMillis();
        
        IssRequest request = createBasicSearchRequest(tenantId, "*");
        
        return webClient.post()
                .uri("/api/v1/search/flows")
                .contentType(MediaType.APPLICATION_JSON)
                .bodyValue(request)
                .retrieve()
                .bodyToMono(JsonNode.class)
                .map(response -> {
                    long executionTime = System.currentTimeMillis() - startTime;
                    return validateSearchResponse(response, "Basic Search", tenantId, executionTime);
                })
                .retryWhen(Retry.backoff(config.getIssConfig().getMaxRetries(), 
                        config.getIssConfig().getRetryDelay()))
                .onErrorReturn(createErrorResult("Basic Search", tenantId, startTime));
    }

    /**
     * Test IP address search
     */
    private Mono<TestResult> testIpAddressSearch(WebClient webClient, String tenantId) {
        long startTime = System.currentTimeMillis();
        
        IssRequest request = createBasicSearchRequest(tenantId, "source_ip:******** OR destination_ip:*******");
        
        return webClient.post()
                .uri("/api/v1/search/flows")
                .contentType(MediaType.APPLICATION_JSON)
                .bodyValue(request)
                .retrieve()
                .bodyToMono(JsonNode.class)
                .map(response -> {
                    long executionTime = System.currentTimeMillis() - startTime;
                    return validateSearchResponse(response, "IP Address Search", tenantId, executionTime);
                })
                .retryWhen(Retry.backoff(config.getIssConfig().getMaxRetries(), 
                        config.getIssConfig().getRetryDelay()))
                .onErrorReturn(createErrorResult("IP Address Search", tenantId, startTime));
    }

    /**
     * Test port search
     */
    private Mono<TestResult> testPortSearch(WebClient webClient, String tenantId) {
        long startTime = System.currentTimeMillis();
        
        IssRequest request = createBasicSearchRequest(tenantId, "port:443 OR port:80");
        
        return webClient.post()
                .uri("/api/v1/search/flows")
                .contentType(MediaType.APPLICATION_JSON)
                .bodyValue(request)
                .retrieve()
                .bodyToMono(JsonNode.class)
                .map(response -> {
                    long executionTime = System.currentTimeMillis() - startTime;
                    return validateSearchResponse(response, "Port Search", tenantId, executionTime);
                })
                .retryWhen(Retry.backoff(config.getIssConfig().getMaxRetries(), 
                        config.getIssConfig().getRetryDelay()))
                .onErrorReturn(createErrorResult("Port Search", tenantId, startTime));
    }

    /**
     * Test protocol search
     */
    private Mono<TestResult> testProtocolSearch(WebClient webClient, String tenantId) {
        long startTime = System.currentTimeMillis();
        
        IssRequest request = createBasicSearchRequest(tenantId, "protocol:tcp");
        
        return webClient.post()
                .uri("/api/v1/search/flows")
                .contentType(MediaType.APPLICATION_JSON)
                .bodyValue(request)
                .retrieve()
                .bodyToMono(JsonNode.class)
                .map(response -> {
                    long executionTime = System.currentTimeMillis() - startTime;
                    return validateSearchResponse(response, "Protocol Search", tenantId, executionTime);
                })
                .retryWhen(Retry.backoff(config.getIssConfig().getMaxRetries(), 
                        config.getIssConfig().getRetryDelay()))
                .onErrorReturn(createErrorResult("Protocol Search", tenantId, startTime));
    }

    /**
     * Test time range search
     */
    private Mono<TestResult> testTimeRangeSearch(WebClient webClient, String tenantId) {
        long startTime = System.currentTimeMillis();
        
        IssRequest request = createTimeRangeSearchRequest(tenantId);
        
        return webClient.post()
                .uri("/api/v1/search/flows")
                .contentType(MediaType.APPLICATION_JSON)
                .bodyValue(request)
                .retrieve()
                .bodyToMono(JsonNode.class)
                .map(response -> {
                    long executionTime = System.currentTimeMillis() - startTime;
                    return validateSearchResponse(response, "Time Range Search", tenantId, executionTime);
                })
                .retryWhen(Retry.backoff(config.getIssConfig().getMaxRetries(), 
                        config.getIssConfig().getRetryDelay()))
                .onErrorReturn(createErrorResult("Time Range Search", tenantId, startTime));
    }

    private WebClient createWebClient() {
        WebClient.Builder builder = webClientBuilder
                .baseUrl(config.getIssConfig().getBaseUrl())
                .defaultHeader(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_JSON_VALUE);

        if (config.getIssConfig().getAuthKey() != null) {
            builder.defaultHeader("x-auth-key", config.getIssConfig().getAuthKey());
        }

        return builder.build();
    }

    private IssRequest createBasicSearchRequest(String tenantId, String query) {
        Instant endTime = Instant.now();
        Instant startTime = endTime.minus(config.getTestConfig().getTestTimeRange());
        
        return IssRequest.builder()
                .query(query)
                .timeFrame(IssRequest.TimeFrame.builder()
                        .startTime(startTime)
                        .endTime(endTime)
                        .build())
                .filters(List.of(
                        IssRequest.Filter.builder()
                                .field("tenantId")
                                .operator("equals")
                                .value(tenantId)
                                .build()
                ))
                .pagination(IssRequest.Pagination.builder()
                        .offset(0)
                        .limit(100)
                        .build())
                .sortBy("timestamp")
                .sortOrder("desc")
                .build();
    }

    private IssRequest createTimeRangeSearchRequest(String tenantId) {
        Instant endTime = Instant.now();
        Instant startTime = endTime.minus(config.getTestConfig().getTestTimeRange());
        
        return IssRequest.builder()
                .query("*")
                .timeFrame(IssRequest.TimeFrame.builder()
                        .startTime(startTime)
                        .endTime(endTime)
                        .build())
                .filters(List.of(
                        IssRequest.Filter.builder()
                                .field("tenantId")
                                .operator("equals")
                                .value(tenantId)
                                .build(),
                        IssRequest.Filter.builder()
                                .field("timestamp")
                                .operator("greater_than")
                                .value(startTime.toString())
                                .build()
                ))
                .pagination(IssRequest.Pagination.builder()
                        .offset(0)
                        .limit(50)
                        .build())
                .build();
    }

    private TestResult validateSearchResponse(JsonNode response, String testName, String tenantId, long executionTime) {
        List<TestResult.ValidationResult> validations = new ArrayList<>();
        Map<String, Object> metrics = new HashMap<>();
        
        // Extract metrics from response
        long resultCount = extractResultCount(response);
        long totalBytes = extractTotalBytes(response);
        boolean hasResults = resultCount > 0;
        
        metrics.put("resultCount", resultCount);
        metrics.put("totalBytes", totalBytes);
        metrics.put("responseSize", response.toString().length());
        metrics.put("hasResults", hasResults);
        
        // Validate result count
        boolean resultCountValid = resultCount >= config.getTestConfig().getMinFlowCount();
        validations.add(TestResult.ValidationResult.builder()
                .validationType("resultCount")
                .passed(resultCountValid)
                .expectedValue(">= " + config.getTestConfig().getMinFlowCount())
                .actualValue(String.valueOf(resultCount))
                .message(resultCountValid ? "Result count validation passed" : "Result count below minimum threshold")
                .build());
        
        // Validate byte count
        boolean byteCountValid = totalBytes >= config.getTestConfig().getMinByteCount();
        validations.add(TestResult.ValidationResult.builder()
                .validationType("totalBytes")
                .passed(byteCountValid)
                .expectedValue(">= " + config.getTestConfig().getMinByteCount())
                .actualValue(String.valueOf(totalBytes))
                .message(byteCountValid ? "Byte count validation passed" : "Byte count below minimum threshold")
                .build());
        
        // Validate response structure
        boolean responseStructureValid = response.has("results") || response.has("data") || response.has("flows");
        validations.add(TestResult.ValidationResult.builder()
                .validationType("responseStructure")
                .passed(responseStructureValid)
                .expectedValue("Valid response structure")
                .actualValue(responseStructureValid ? "Valid" : "Invalid")
                .message(responseStructureValid ? "Response structure is valid" : "Response structure is invalid")
                .build());
        
        // Determine overall test status
        boolean allValidationsPassed = validations.stream().allMatch(TestResult.ValidationResult::isPassed);
        TestResult.TestStatus status = allValidationsPassed ? TestResult.TestStatus.PASSED : TestResult.TestStatus.FAILED;
        
        return TestResult.builder()
                .testName(testName)
                .testType("ISS")
                .status(status)
                .tenantId(tenantId)
                .timestamp(Instant.now())
                .executionTime(TestResult.Duration.ofMillis(executionTime))
                .metrics(metrics)
                .validations(validations)
                .build();
    }

    private long extractResultCount(JsonNode response) {
        // Extract result count from response - adjust path based on actual response structure
        if (response.has("totalCount")) {
            return response.get("totalCount").asLong();
        }
        if (response.has("results") && response.get("results").isArray()) {
            return response.get("results").size();
        }
        if (response.has("data") && response.get("data").has("total")) {
            return response.get("data").get("total").asLong();
        }
        if (response.has("flows") && response.get("flows").isArray()) {
            return response.get("flows").size();
        }
        return 0;
    }

    private long extractTotalBytes(JsonNode response) {
        // Extract total bytes from response - adjust path based on actual response structure
        if (response.has("totalBytes")) {
            return response.get("totalBytes").asLong();
        }
        if (response.has("data") && response.get("data").has("totalBytes")) {
            return response.get("data").get("totalBytes").asLong();
        }
        
        // Sum up bytes from individual results if available
        JsonNode results = null;
        if (response.has("results")) {
            results = response.get("results");
        } else if (response.has("flows")) {
            results = response.get("flows");
        } else if (response.has("data") && response.get("data").has("flows")) {
            results = response.get("data").get("flows");
        }
        
        if (results != null && results.isArray()) {
            long totalBytes = 0;
            for (JsonNode result : results) {
                if (result.has("sentBytes")) {
                    totalBytes += result.get("sentBytes").asLong();
                }
                if (result.has("receivedBytes")) {
                    totalBytes += result.get("receivedBytes").asLong();
                }
                if (result.has("totalBytes")) {
                    totalBytes += result.get("totalBytes").asLong();
                }
            }
            return totalBytes;
        }
        
        return 0;
    }

    private TestResult createErrorResult(String testName, String tenantId, long startTime) {
        long executionTime = System.currentTimeMillis() - startTime;
        return TestResult.builder()
                .testName(testName)
                .testType("ISS")
                .status(TestResult.TestStatus.ERROR)
                .tenantId(tenantId)
                .timestamp(Instant.now())
                .executionTime(TestResult.Duration.ofMillis(executionTime))
                .errorMessage("API call failed")
                .build();
    }
}
