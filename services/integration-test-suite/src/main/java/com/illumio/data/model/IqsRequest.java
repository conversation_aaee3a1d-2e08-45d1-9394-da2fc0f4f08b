package com.illumio.data.model;

import com.fasterxml.jackson.annotation.JsonProperty;

import java.time.Instant;
import java.util.List;

/**
 * Request models for IQS API calls
 */
public class IqsRequest {
    @JsonProperty("currentTimeFrame")
    private TimeFrame currentTimeFrame;

    @JsonProperty("comparisonTimeFrame")
    private TimeFrame comparisonTimeFrame;

    @JsonProperty("filters")
    private List<Filter> filters;

    @JsonProperty("sortByFields")
    private List<SortByField> sortByFields;

    @JsonProperty("pagination")
    private Pagination pagination;

    // Constructors
    public IqsRequest() {}

    public IqsRequest(TimeFrame currentTimeFrame, TimeFrame comparisonTimeFrame,
                     List<Filter> filters, List<SortByField> sortByFields, Pagination pagination) {
        this.currentTimeFrame = currentTimeFrame;
        this.comparisonTimeFrame = comparisonTimeFrame;
        this.filters = filters;
        this.sortByFields = sortByFields;
        this.pagination = pagination;
    }

    // Getters and Setters
    public TimeFrame getCurrentTimeFrame() { return currentTimeFrame; }
    public void setCurrentTimeFrame(TimeFrame currentTimeFrame) { this.currentTimeFrame = currentTimeFrame; }

    public TimeFrame getComparisonTimeFrame() { return comparisonTimeFrame; }
    public void setComparisonTimeFrame(TimeFrame comparisonTimeFrame) { this.comparisonTimeFrame = comparisonTimeFrame; }

    public List<Filter> getFilters() { return filters; }
    public void setFilters(List<Filter> filters) { this.filters = filters; }

    public List<SortByField> getSortByFields() { return sortByFields; }
    public void setSortByFields(List<SortByField> sortByFields) { this.sortByFields = sortByFields; }

    public Pagination getPagination() { return pagination; }
    public void setPagination(Pagination pagination) { this.pagination = pagination; }

    // Builder pattern
    public static Builder builder() {
        return new Builder();
    }

    public static class Builder {
        private TimeFrame currentTimeFrame;
        private TimeFrame comparisonTimeFrame;
        private List<Filter> filters;
        private List<SortByField> sortByFields;
        private Pagination pagination;

        public Builder currentTimeFrame(TimeFrame currentTimeFrame) { this.currentTimeFrame = currentTimeFrame; return this; }
        public Builder comparisonTimeFrame(TimeFrame comparisonTimeFrame) { this.comparisonTimeFrame = comparisonTimeFrame; return this; }
        public Builder filters(List<Filter> filters) { this.filters = filters; return this; }
        public Builder sortByFields(List<SortByField> sortByFields) { this.sortByFields = sortByFields; return this; }
        public Builder pagination(Pagination pagination) { this.pagination = pagination; return this; }

        public IqsRequest build() {
            return new IqsRequest(currentTimeFrame, comparisonTimeFrame, filters, sortByFields, pagination);
        }
    }

    public static class TimeFrame {
        @JsonProperty("startTime")
        private Instant startTime;

        @JsonProperty("endTime")
        private Instant endTime;

        // Constructors
        public TimeFrame() {}

        public TimeFrame(Instant startTime, Instant endTime) {
            this.startTime = startTime;
            this.endTime = endTime;
        }

        // Getters and Setters
        public Instant getStartTime() { return startTime; }
        public void setStartTime(Instant startTime) { this.startTime = startTime; }

        public Instant getEndTime() { return endTime; }
        public void setEndTime(Instant endTime) { this.endTime = endTime; }

        // Builder pattern
        public static Builder builder() {
            return new Builder();
        }

        public static class Builder {
            private Instant startTime;
            private Instant endTime;

            public Builder startTime(Instant startTime) { this.startTime = startTime; return this; }
            public Builder endTime(Instant endTime) { this.endTime = endTime; return this; }

            public TimeFrame build() {
                return new TimeFrame(startTime, endTime);
            }
        }
    }

    public static class Filter {
        @JsonProperty("categoryName")
        private String categoryName;

        @JsonProperty("categoryType")
        private String categoryType;

        @JsonProperty("categoryValue")
        private List<Object> categoryValue;

        // Constructors
        public Filter() {}

        public Filter(String categoryName, String categoryType, List<Object> categoryValue) {
            this.categoryName = categoryName;
            this.categoryType = categoryType;
            this.categoryValue = categoryValue;
        }

        // Getters and Setters
        public String getCategoryName() { return categoryName; }
        public void setCategoryName(String categoryName) { this.categoryName = categoryName; }

        public String getCategoryType() { return categoryType; }
        public void setCategoryType(String categoryType) { this.categoryType = categoryType; }

        public List<Object> getCategoryValue() { return categoryValue; }
        public void setCategoryValue(List<Object> categoryValue) { this.categoryValue = categoryValue; }

        // Builder pattern
        public static Builder builder() {
            return new Builder();
        }

        public static class Builder {
            private String categoryName;
            private String categoryType;
            private List<Object> categoryValue;

            public Builder categoryName(String categoryName) { this.categoryName = categoryName; return this; }
            public Builder categoryType(String categoryType) { this.categoryType = categoryType; return this; }
            public Builder categoryValue(List<Object> categoryValue) { this.categoryValue = categoryValue; return this; }

            public Filter build() {
                return new Filter(categoryName, categoryType, categoryValue);
            }
        }
    }

    public static class SortByField {
        @JsonProperty("field")
        private String field;

        @JsonProperty("order")
        private String order;  // "asc" or "desc"

        // Constructors
        public SortByField() {}

        public SortByField(String field, String order) {
            this.field = field;
            this.order = order;
        }

        // Getters and Setters
        public String getField() { return field; }
        public void setField(String field) { this.field = field; }

        public String getOrder() { return order; }
        public void setOrder(String order) { this.order = order; }

        // Builder pattern
        public static Builder builder() {
            return new Builder();
        }

        public static class Builder {
            private String field;
            private String order;

            public Builder field(String field) { this.field = field; return this; }
            public Builder order(String order) { this.order = order; return this; }

            public SortByField build() {
                return new SortByField(field, order);
            }
        }
    }

    public static class Pagination {
        @JsonProperty("page")
        private int page;

        @JsonProperty("size")
        private int size;

        // Constructors
        public Pagination() {}

        public Pagination(int page, int size) {
            this.page = page;
            this.size = size;
        }

        // Getters and Setters
        public int getPage() { return page; }
        public void setPage(int page) { this.page = page; }

        public int getSize() { return size; }
        public void setSize(int size) { this.size = size; }

        // Builder pattern
        public static Builder builder() {
            return new Builder();
        }

        public static class Builder {
            private int page;
            private int size;

            public Builder page(int page) { this.page = page; return this; }
            public Builder size(int size) { this.size = size; return this; }

            public Pagination build() {
                return new Pagination(page, size);
            }
        }
    }
}
