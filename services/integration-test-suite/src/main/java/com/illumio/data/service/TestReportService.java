package com.illumio.data.service;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.illumio.data.model.TestResult;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;

import java.time.Instant;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * Service for generating and managing test reports
 */
@Service
public class TestReportService {

    private static final Logger log = LoggerFactory.getLogger(TestReportService.class);

    private final ObjectMapper objectMapper;

    public TestReportService(ObjectMapper objectMapper) {
        this.objectMapper = objectMapper;
    }

    /**
     * Generates a comprehensive test report
     */
    public Mono<TestReport> generateReport(List<TestResult> testResults) {
        return Mono.fromCallable(() -> {
            log.info("Generating test report for {} test results", testResults.size());
            
            TestReport report = TestReport.builder()
                    .reportId(generateReportId())
                    .timestamp(Instant.now())
                    .totalTests(testResults.size())
                    .passedTests(countByStatus(testResults, TestResult.TestStatus.PASSED))
                    .failedTests(countByStatus(testResults, TestResult.TestStatus.FAILED))
                    .errorTests(countByStatus(testResults, TestResult.TestStatus.ERROR))
                    .skippedTests(countByStatus(testResults, TestResult.TestStatus.SKIPPED))
                    .testResults(testResults)
                    .summary(generateSummary(testResults))
                    .recommendations(generateRecommendations(testResults))
                    .build();
            
            log.info("Generated test report: {} total tests, {} passed, {} failed, {} errors", 
                    report.getTotalTests(), report.getPassedTests(), 
                    report.getFailedTests(), report.getErrorTests());
            
            return report;
        });
    }

    /**
     * Generates a summary of test results
     */
    private TestSummary generateSummary(List<TestResult> testResults) {
        Map<String, Long> testsByType = testResults.stream()
                .collect(Collectors.groupingBy(TestResult::getTestType, Collectors.counting()));
        
        Map<String, Long> testsByTenant = testResults.stream()
                .collect(Collectors.groupingBy(TestResult::getTenantId, Collectors.counting()));
        
        double successRate = testResults.isEmpty() ? 0.0 : 
                (double) countByStatus(testResults, TestResult.TestStatus.PASSED) / testResults.size() * 100;
        
        long avgExecutionTime = testResults.stream()
                .mapToLong(r -> r.getExecutionTime() != null ? r.getExecutionTime().getMilliseconds() : 0)
                .sum() / Math.max(testResults.size(), 1);
        
        return TestSummary.builder()
                .successRate(successRate)
                .averageExecutionTime(avgExecutionTime)
                .testsByType(testsByType)
                .testsByTenant(testsByTenant)
                .criticalIssues(identifyCriticalIssues(testResults))
                .build();
    }

    /**
     * Generates recommendations based on test results
     */
    private List<String> generateRecommendations(List<TestResult> testResults) {
        List<String> recommendations = new ArrayList<>();
        
        long failedTests = countByStatus(testResults, TestResult.TestStatus.FAILED);
        long errorTests = countByStatus(testResults, TestResult.TestStatus.ERROR);
        
        if (failedTests > 0) {
            recommendations.add("Investigate " + failedTests + " failed tests - check if pipeline is processing data correctly");
        }
        
        if (errorTests > 0) {
            recommendations.add("Resolve " + errorTests + " test errors - check service connectivity and authentication");
        }
        
        // Check for specific validation failures
        boolean hasFlowCountIssues = testResults.stream()
                .flatMap(r -> r.getValidations() != null ? r.getValidations().stream() : Stream.empty())
                .anyMatch(v -> "flowCount".equals(v.getValidationType()) && !v.isPassed());
        
        if (hasFlowCountIssues) {
            recommendations.add("Flow count validation failed - verify data ingestion pipeline is working");
        }
        
        boolean hasByteCountIssues = testResults.stream()
                .flatMap(r -> r.getValidations() != null ? r.getValidations().stream() : Stream.empty())
                .anyMatch(v -> ("byteCount".equals(v.getValidationType()) || "totalBytes".equals(v.getValidationType())) && !v.isPassed());
        
        if (hasByteCountIssues) {
            recommendations.add("Byte count validation failed - check data aggregation and calculation logic");
        }
        
        // Check for performance issues
        long avgExecutionTime = testResults.stream()
                .mapToLong(r -> r.getExecutionTime() != null ? r.getExecutionTime().getMilliseconds() : 0)
                .sum() / Math.max(testResults.size(), 1);
        
        if (avgExecutionTime > 10000) { // More than 10 seconds
            recommendations.add("High average execution time (" + avgExecutionTime + "ms) - consider performance optimization");
        }
        
        if (recommendations.isEmpty()) {
            recommendations.add("All tests passed successfully - pipeline is functioning correctly");
        }
        
        return recommendations;
    }

    private List<String> identifyCriticalIssues(List<TestResult> testResults) {
        List<String> issues = new ArrayList<>();
        
        // Check if all tests for a tenant failed
        Map<String, List<TestResult>> resultsByTenant = testResults.stream()
                .collect(Collectors.groupingBy(TestResult::getTenantId));
        
        resultsByTenant.forEach((tenantId, results) -> {
            boolean allFailed = results.stream()
                    .allMatch(r -> r.getStatus() == TestResult.TestStatus.FAILED || r.getStatus() == TestResult.TestStatus.ERROR);
            
            if (allFailed && !results.isEmpty()) {
                issues.add("All tests failed for tenant: " + tenantId);
            }
        });
        
        // Check if all IQS or ISS tests failed
        Map<String, List<TestResult>> resultsByType = testResults.stream()
                .collect(Collectors.groupingBy(TestResult::getTestType));
        
        resultsByType.forEach((type, results) -> {
            boolean allFailed = results.stream()
                    .allMatch(r -> r.getStatus() == TestResult.TestStatus.FAILED || r.getStatus() == TestResult.TestStatus.ERROR);
            
            if (allFailed && !results.isEmpty()) {
                issues.add("All " + type + " tests failed - service may be down");
            }
        });
        
        return issues;
    }

    private long countByStatus(List<TestResult> testResults, TestResult.TestStatus status) {
        return testResults.stream()
                .mapToLong(r -> r.getStatus() == status ? 1 : 0)
                .sum();
    }

    private String generateReportId() {
        return "report-" + Instant.now().toString().replaceAll("[:.\\-]", "");
    }

    @lombok.Data
    @lombok.Builder
    public static class TestReport {
        private String reportId;
        private Instant timestamp;
        private int totalTests;
        private long passedTests;
        private long failedTests;
        private long errorTests;
        private long skippedTests;
        private List<TestResult> testResults;
        private TestSummary summary;
        private List<String> recommendations;
    }

    @lombok.Data
    @lombok.Builder
    public static class TestSummary {
        private double successRate;
        private long averageExecutionTime;
        private Map<String, Long> testsByType;
        private Map<String, Long> testsByTenant;
        private List<String> criticalIssues;
    }
}
