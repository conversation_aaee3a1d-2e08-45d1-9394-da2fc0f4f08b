package com.illumio.data.model;

import com.fasterxml.jackson.annotation.JsonProperty;

import java.time.Instant;
import java.util.List;

/**
 * Request models for ISS API calls
 */
public class IssRequest {
    @JsonProperty("query")
    private String query;

    @JsonProperty("timeFrame")
    private TimeFrame timeFrame;

    @JsonProperty("filters")
    private List<Filter> filters;

    @JsonProperty("pagination")
    private Pagination pagination;

    @JsonProperty("sortBy")
    private String sortBy;

    @JsonProperty("sortOrder")
    private String sortOrder;

    // Constructors
    public IssRequest() {}

    public IssRequest(String query, TimeFrame timeFrame, List<Filter> filters,
                     Pagination pagination, String sortBy, String sortOrder) {
        this.query = query;
        this.timeFrame = timeFrame;
        this.filters = filters;
        this.pagination = pagination;
        this.sortBy = sortBy;
        this.sortOrder = sortOrder;
    }

    // Getters and Setters
    public String getQuery() { return query; }
    public void setQuery(String query) { this.query = query; }

    public TimeFrame getTimeFrame() { return timeFrame; }
    public void setTimeFrame(TimeFrame timeFrame) { this.timeFrame = timeFrame; }

    public List<Filter> getFilters() { return filters; }
    public void setFilters(List<Filter> filters) { this.filters = filters; }

    public Pagination getPagination() { return pagination; }
    public void setPagination(Pagination pagination) { this.pagination = pagination; }

    public String getSortBy() { return sortBy; }
    public void setSortBy(String sortBy) { this.sortBy = sortBy; }

    public String getSortOrder() { return sortOrder; }
    public void setSortOrder(String sortOrder) { this.sortOrder = sortOrder; }

    // Builder pattern
    public static Builder builder() {
        return new Builder();
    }

    public static class Builder {
        private String query;
        private TimeFrame timeFrame;
        private List<Filter> filters;
        private Pagination pagination;
        private String sortBy;
        private String sortOrder;

        public Builder query(String query) { this.query = query; return this; }
        public Builder timeFrame(TimeFrame timeFrame) { this.timeFrame = timeFrame; return this; }
        public Builder filters(List<Filter> filters) { this.filters = filters; return this; }
        public Builder pagination(Pagination pagination) { this.pagination = pagination; return this; }
        public Builder sortBy(String sortBy) { this.sortBy = sortBy; return this; }
        public Builder sortOrder(String sortOrder) { this.sortOrder = sortOrder; return this; }

        public IssRequest build() {
            return new IssRequest(query, timeFrame, filters, pagination, sortBy, sortOrder);
        }
    }

    public static class TimeFrame {
        @JsonProperty("startTime")
        private Instant startTime;

        @JsonProperty("endTime")
        private Instant endTime;

        // Constructors
        public TimeFrame() {}

        public TimeFrame(Instant startTime, Instant endTime) {
            this.startTime = startTime;
            this.endTime = endTime;
        }

        // Getters and Setters
        public Instant getStartTime() { return startTime; }
        public void setStartTime(Instant startTime) { this.startTime = startTime; }

        public Instant getEndTime() { return endTime; }
        public void setEndTime(Instant endTime) { this.endTime = endTime; }

        // Builder pattern
        public static Builder builder() {
            return new Builder();
        }

        public static class Builder {
            private Instant startTime;
            private Instant endTime;

            public Builder startTime(Instant startTime) { this.startTime = startTime; return this; }
            public Builder endTime(Instant endTime) { this.endTime = endTime; return this; }

            public TimeFrame build() {
                return new TimeFrame(startTime, endTime);
            }
        }
    }

    public static class Filter {
        @JsonProperty("field")
        private String field;

        @JsonProperty("operator")
        private String operator;  // "equals", "contains", "greater_than", etc.

        @JsonProperty("value")
        private Object value;

        // Constructors
        public Filter() {}

        public Filter(String field, String operator, Object value) {
            this.field = field;
            this.operator = operator;
            this.value = value;
        }

        // Getters and Setters
        public String getField() { return field; }
        public void setField(String field) { this.field = field; }

        public String getOperator() { return operator; }
        public void setOperator(String operator) { this.operator = operator; }

        public Object getValue() { return value; }
        public void setValue(Object value) { this.value = value; }

        // Builder pattern
        public static Builder builder() {
            return new Builder();
        }

        public static class Builder {
            private String field;
            private String operator;
            private Object value;

            public Builder field(String field) { this.field = field; return this; }
            public Builder operator(String operator) { this.operator = operator; return this; }
            public Builder value(Object value) { this.value = value; return this; }

            public Filter build() {
                return new Filter(field, operator, value);
            }
        }
    }

    public static class Pagination {
        @JsonProperty("offset")
        private int offset;

        @JsonProperty("limit")
        private int limit;

        // Constructors
        public Pagination() {}

        public Pagination(int offset, int limit) {
            this.offset = offset;
            this.limit = limit;
        }

        // Getters and Setters
        public int getOffset() { return offset; }
        public void setOffset(int offset) { this.offset = offset; }

        public int getLimit() { return limit; }
        public void setLimit(int limit) { this.limit = limit; }

        // Builder pattern
        public static Builder builder() {
            return new Builder();
        }

        public static class Builder {
            private int offset;
            private int limit;

            public Builder offset(int offset) { this.offset = offset; return this; }
            public Builder limit(int limit) { this.limit = limit; return this; }

            public Pagination build() {
                return new Pagination(offset, limit);
            }
        }
    }
}
