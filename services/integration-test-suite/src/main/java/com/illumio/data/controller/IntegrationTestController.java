package com.illumio.data.controller;

import com.illumio.data.model.TestResult;
import com.illumio.data.service.IntegrationTestOrchestrator;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import reactor.core.publisher.Mono;

import java.time.Instant;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * REST controller for managing and monitoring integration tests
 */
@RestController
@RequestMapping("/api/v1/integration-tests")
public class IntegrationTestController {

    private static final Logger log = LoggerFactory.getLogger(IntegrationTestController.class);

    private final IntegrationTestOrchestrator testOrchestrator;

    public IntegrationTestController(IntegrationTestOrchestrator testOrchestrator) {
        this.testOrchestrator = testOrchestrator;
    }

    /**
     * Get the current status of the integration test suite
     */
    @GetMapping("/status")
    public ResponseEntity<Map<String, Object>> getStatus() {
        IntegrationTestOrchestrator.TestExecutionStats stats = testOrchestrator.getExecutionStats();

        Map<String, Object> response = new HashMap<>();
        response.put("status", testOrchestrator.getStatus());
        response.put("totalExecutions", stats.getTotalExecutions());
        response.put("nextExecutionTime", stats.getNextExecutionTime());
        response.put("timestamp", Instant.now());
        response.put("service", "integration-test-suite");

        return ResponseEntity.ok(response);
    }

    /**
     * Manually trigger a complete test suite execution
     */
    @PostMapping("/execute")
    public Mono<ResponseEntity<Map<String, Object>>> executeTests() {
        log.info("Manual execution of integration test suite requested");
        
        return testOrchestrator.executeOnce()
                .map(results -> {
                    long passedTests = results.stream()
                            .mapToLong(r -> r.getStatus() == TestResult.TestStatus.PASSED ? 1 : 0)
                            .sum();
                    long failedTests = results.stream()
                            .mapToLong(r -> r.getStatus() == TestResult.TestStatus.FAILED ? 1 : 0)
                            .sum();
                    long errorTests = results.stream()
                            .mapToLong(r -> r.getStatus() == TestResult.TestStatus.ERROR ? 1 : 0)
                            .sum();
                    
                    Map<String, Object> response = new HashMap<>();
                    response.put("message", "Integration test suite executed successfully");
                    response.put("timestamp", Instant.now());
                    response.put("totalTests", results.size());
                    response.put("passedTests", passedTests);
                    response.put("failedTests", failedTests);
                    response.put("errorTests", errorTests);
                    response.put("status", "completed");

                    return ResponseEntity.ok(response);
                })
                .onErrorResume(error -> {
                    Map<String, Object> errorResponse = new HashMap<>();
                    errorResponse.put("message", "Integration test suite execution failed");
                    errorResponse.put("timestamp", Instant.now());
                    errorResponse.put("status", "error");
                    return Mono.just(ResponseEntity.status(500).body(errorResponse));
                });
    }

    /**
     * Execute tests for a specific tenant
     */
    @PostMapping("/execute/{tenantId}")
    public Mono<ResponseEntity<Map<String, Object>>> executeTestsForTenant(@PathVariable String tenantId) {
        log.info("Manual execution of integration tests requested for tenant: {}", tenantId);
        
        // This would require adding a method to the orchestrator to test a specific tenant
        Map<String, Object> response = new HashMap<>();
        response.put("message", "Tenant-specific test execution not yet implemented");
        response.put("tenantId", tenantId);
        response.put("timestamp", Instant.now());

        return Mono.just(ResponseEntity.ok(response));
    }

    /**
     * Get test execution history/statistics
     */
    @GetMapping("/stats")
    public ResponseEntity<Map<String, Object>> getExecutionStats() {
        IntegrationTestOrchestrator.TestExecutionStats stats = testOrchestrator.getExecutionStats();
        
        Map<String, Object> response = new HashMap<>();
        response.put("totalExecutions", stats.getTotalExecutions());
        response.put("status", stats.getStatus());
        response.put("nextExecutionTime", stats.getNextExecutionTime());
        response.put("timestamp", Instant.now());

        return ResponseEntity.ok(response);
    }

    /**
     * Health check endpoint
     */
    @GetMapping("/health")
    public ResponseEntity<Map<String, Object>> health() {
        Map<String, Object> response = new HashMap<>();
        response.put("status", "UP");
        response.put("orchestrator", testOrchestrator.getStatus());
        response.put("timestamp", Instant.now());

        return ResponseEntity.ok(response);
    }

    /**
     * Get basic pipeline health check
     */
    @GetMapping("/pipeline-health")
    public Mono<ResponseEntity<Map<String, Object>>> getPipelineHealth() {
        log.info("Pipeline health check requested");
        
        return testOrchestrator.executeOnce()
                .map(results -> {
                    boolean pipelineHealthy = results.stream()
                            .anyMatch(r -> r.getStatus() == TestResult.TestStatus.PASSED);
                    
                    long totalFlowCount = results.stream()
                            .filter(r -> r.getMetrics() != null && r.getMetrics().containsKey("flowCount"))
                            .mapToLong(r -> ((Number) r.getMetrics().get("flowCount")).longValue())
                            .sum();
                    
                    long totalByteCount = results.stream()
                            .filter(r -> r.getMetrics() != null && 
                                    (r.getMetrics().containsKey("byteCount") || r.getMetrics().containsKey("totalBytes")))
                            .mapToLong(r -> {
                                Object bytes = r.getMetrics().get("byteCount");
                                if (bytes == null) {
                                    bytes = r.getMetrics().get("totalBytes");
                                }
                                return bytes != null ? ((Number) bytes).longValue() : 0;
                            })
                            .sum();
                    
                    Map<String, Object> response = new HashMap<>();
                    response.put("pipelineHealthy", pipelineHealthy);
                    response.put("totalFlowCount", totalFlowCount);
                    response.put("totalByteCount", totalByteCount);
                    response.put("flowCountPositive", totalFlowCount > 0);
                    response.put("byteCountPositive", totalByteCount > 0);
                    response.put("timestamp", Instant.now());
                    response.put("message", pipelineHealthy ? "Pipeline is processing data" : "Pipeline may have issues");

                    return ResponseEntity.ok(response);
                })
                .onErrorResume(error -> {
                    Map<String, Object> errorResponse = new HashMap<>();
                    errorResponse.put("pipelineHealthy", false);
                    errorResponse.put("message", "Failed to check pipeline health");
                    errorResponse.put("timestamp", Instant.now());
                    return Mono.just(ResponseEntity.status(500).body(errorResponse));
                });
    }
}
