package com.illumio.data;

import com.illumio.data.service.IntegrationTestOrchestrator;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.context.event.ApplicationReadyEvent;
import org.springframework.context.event.ContextClosedEvent;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;

/**
 * Handles application lifecycle events to start and stop the integration test orchestrator
 */
@Component
public class ApplicationListener {

    private static final Logger log = LoggerFactory.getLogger(ApplicationListener.class);

    private final IntegrationTestOrchestrator testOrchestrator;

    public ApplicationListener(IntegrationTestOrchestrator testOrchestrator) {
        this.testOrchestrator = testOrchestrator;
    }

    @EventListener(ApplicationReadyEvent.class)
    public void handleApplicationReady() {
        log.info("=== Integration Test Suite Starting ===");
        log.info("This service will test IQS and ISS APIs to validate that the Illumio pipeline is working correctly by checking flowcount > 0 and bytecount > 0.");
        log.info("==========================================");
        testOrchestrator.start();
        
        log.info("Integration Test Suite is now running");
        log.info("Orchestrator status: {}", testOrchestrator.getStatus());
    }

    @EventListener(ContextClosedEvent.class)
    public void handleApplicationShutdown() {
        log.info("Shutting down Integration Test Suite...");
        testOrchestrator.stop();
        log.info("Integration Test Suite stopped");
    }
}
