logging:
  level:
    ROOT: INFO
    com.illumio.data: DEBUG
    org.springframework.web.reactive.function.client: DEBUG

spring:
  application:
    name: integration-test-suite
  output:
    ansi:
      enabled: ALWAYS
  main:
    web-application-type: reactive

server:
  port: 8096

management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics,prometheus
  endpoint:
    health:
      show-details: always
  metrics:
    export:
      prometheus:
        enabled: true

# Integration Test Suite Configuration
integration-test-suite:
  # IQS (Insights Query Service) Configuration
  iqsConfig:
    baseUrl: https://insights.sunnyvale.ilabs.io  # Sunnyvale environment
    authKey: AIzaSyCwEro-wQ6YUNcA1ozA9FQev-DyJp3t2EQ        # Auth key from IQS service
    timeout: 30s
    maxRetries: 3
    retryDelay: 2s

  # ISS (Insights Search Service) Configuration
  issConfig:
    baseUrl: https://insights-search.sunnyvale.ilabs.io  # Sunnyvale ISS service URL
    authKey: AIzaSyCwEro-wQ6YUNcA1ozA9FQev-DyJp3t2EQ       # Auth key from ISS service
    timeout: 30s
    maxRetries: 3
    retryDelay: 2s
    
  # Schedule Configuration
  scheduleConfig:
    enabled: true
    initialDelay: 5m      # Wait 5 minutes after startup
    interval: 2h          # Run every 2 hours
    
  # Test Configuration
  testConfig:
    # Test tenant IDs to validate
    testTenantIds:
      - "39e868b6-fdfc-4118-b664-a7d4b04728e8"  # Sunnyvale environment tenant
      
    # Time ranges for testing
    testTimeRange: 24h        # Look back 24 hours for data
    comparisonTimeRange: 24h  # Compare with previous 24 hours
    
    # Validation thresholds
    minFlowCount: 0           # Minimum expected flow count (0 = any data is good)
    minByteCount: 0           # Minimum expected byte count (0 = any data is good)
    strictValidation: false   # If true, requires flowcount > 0 and bytecount > 0
    
    # Test scenarios - using actual widget IDs from IQS RequestMetadata
    pageId: "1"                    # Static page ID (always 1)
    enableWidgetDiscovery: false   # Use actual widget IDs instead of discovery

    testWidgetIds:
      # Start with just one widget for initial testing
      - "1661"                     # RISKY_TRAFFIC_BY_ROLES_RESOURCE_INSIGHTS (confirmed working from your example)
      
    # Search test queries
    searchQueries:
      - "*"                        # Start with simple wildcard search
