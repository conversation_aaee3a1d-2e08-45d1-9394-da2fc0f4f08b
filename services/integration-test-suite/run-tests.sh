#!/bin/bash

# Manual Test Trigger Script
# Use this script to manually trigger integration tests once the service is running

echo "🧪 Manual Integration Test Trigger"
echo "=================================="

# Check if service is running
echo "🔍 Checking if integration test service is running..."
if curl -s --connect-timeout 5 "http://localhost:8096/api/v1/integration-tests/health" > /dev/null 2>&1; then
    echo "✅ Integration test service is running"
else
    echo "❌ Integration test service is not running on port 8096"
    echo ""
    echo "Please start the service first:"
    echo "   ./services/integration-test-suite/test-sunnyvale.sh"
    exit 1
fi

echo ""
echo "📊 Current Service Status:"
echo "========================="
curl -s "http://localhost:8096/api/v1/integration-tests/status" | jq '.' 2>/dev/null || curl -s "http://localhost:8096/api/v1/integration-tests/status"

echo ""
echo ""
echo "🚀 Triggering Integration Tests..."
echo "================================="
echo "   Testing 1 tenant: 39e868b6-fdfc-4118-b664-a7d4b04728e8"
echo "   Testing 1 IQS widget: 1661 (RISKY_TRAFFIC_BY_ROLES_RESOURCE_INSIGHTS)"
echo "   Testing 1 ISS query: * (wildcard search)"
echo ""

# Trigger the tests
echo "Executing tests... (this may take 30-60 seconds)"
curl -X POST "http://localhost:8096/api/v1/integration-tests/execute" \
     -H "Content-Type: application/json" \
     -w "\n\nHTTP Status: %{http_code}\nTotal Time: %{time_total}s\n" | \
     jq '.' 2>/dev/null || cat

echo ""
echo "✅ Test execution completed!"
echo ""
echo "📋 Additional Commands:"
echo "======================"
echo "   Check pipeline health: curl http://localhost:8096/api/v1/integration-tests/pipeline-health | jq"
echo "   Check service stats: curl http://localhost:8096/api/v1/integration-tests/stats | jq"
echo "   Check service status: curl http://localhost:8096/api/v1/integration-tests/status | jq"
