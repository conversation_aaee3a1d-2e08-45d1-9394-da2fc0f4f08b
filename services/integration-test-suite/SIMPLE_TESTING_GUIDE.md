# Simple Integration Testing Guide

## 🎯 **What This Tests**

The integration test suite is configured for **minimal testing** with:

- **1 Tenant**: `39e868b6-fdfc-4118-b664-a7d4b04728e8` (Sunnyvale)
- **1 IQS Widget**: `1661` (RISKY_TRAFFIC_BY_ROLES_RESOURCE_INSIGHTS)
- **1 ISS Query**: `*` (wildcard search)

## 🚀 **Quick Start**

### Step 1: Start the Integration Test Service

```bash
# From the project root directory
./services/integration-test-suite/test-sunnyvale.sh
```

This will:
- Build the integration test suite
- Start the service on port 8096
- Connect to the Sunnyvale environment:
  - IQS: `https://insights.sunnyvale.ilabs.io`
  - ISS: `https://insights-search.sunnyvale.ilabs.io`

### Step 2: Run the Tests

In a **new terminal**, run:

```bash
# From the project root directory
./services/integration-test-suite/run-tests.sh
```

This will:
- Check if the service is running
- Trigger the integration tests
- Show the results

## 📊 **What Gets Tested**

### IQS Test:
- **Endpoint**: `POST /api/v1/tenant/39e868b6-fdfc-4118-b664-a7d4b04728e8/insights/1/widget/1661`
- **Validates**: Response contains data with `flowCount > 0` and `byteCount > 0`

### ISS Test:
- **Endpoint**: `POST /api/v1/search/flows`
- **Query**: `*` (wildcard search)
- **Validates**: Response contains search results

## 🔍 **Manual API Testing**

Once the service is running on port 8096:

```bash
# Check service status
curl http://localhost:8096/api/v1/integration-tests/status | jq

# Run tests manually
curl -X POST http://localhost:8096/api/v1/integration-tests/execute | jq

# Check pipeline health
curl http://localhost:8096/api/v1/integration-tests/pipeline-health | jq

# Get execution stats
curl http://localhost:8096/api/v1/integration-tests/stats | jq
```

## ✅ **Expected Results**

### Successful Test Output:
```json
{
  "message": "Integration test suite executed successfully",
  "totalTests": 2,
  "passedTests": 2,
  "failedTests": 0,
  "errorTests": 0,
  "status": "completed"
}
```

### Pipeline Health Check:
```json
{
  "pipelineHealthy": true,
  "totalFlowCount": 1234,
  "totalByteCount": 567890,
  "flowCountPositive": true,
  "byteCountPositive": true,
  "message": "Pipeline is processing data"
}
```

## 🛠 **Configuration**

The test configuration is in `services/integration-test-suite/src/main/resources/application.yml`:

```yaml
integration-test-suite:
  testConfig:
    testTenantIds:
      - "39e868b6-fdfc-4118-b664-a7d4b04728e8"
    testWidgetIds:
      - "1661"  # RISKY_TRAFFIC_BY_ROLES_RESOURCE_INSIGHTS
    searchQueries:
      - "*"     # Simple wildcard search
```

## 📈 **Scaling Up Later**

To add more tests later, simply update the configuration:

```yaml
testTenantIds:
  - "39e868b6-fdfc-4118-b664-a7d4b04728e8"
  - "another-tenant-id"

testWidgetIds:
  - "1661"  # Current widget
  - "1234"  # Add more widgets
  - "5678"

searchQueries:
  - "*"                    # Current query
  - "source_ip:********"  # Add more queries
  - "port:443"
```

## 🔧 **Troubleshooting**

### Service Won't Start:
- Check if port 8096 is available
- Verify Java/Gradle installation
- Check logs in the terminal

### Tests Fail:
- Verify Sunnyvale environment connectivity
- Check auth keys in configuration
- Ensure tenant ID and widget ID are valid

### No Data Returned:
- This may be normal if there's no recent traffic
- Check the time range in configuration (currently 24h lookback)
- Verify the tenant has data in the specified time range
