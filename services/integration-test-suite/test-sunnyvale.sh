#!/bin/bash

# Sunnyvale Environment Integration Test Runner
# This script runs the integration test suite against the Sunnyvale environment

echo "🚀 Integration Test Suite - Sunnyvale Environment Testing"
echo "========================================================"

# Check if we're in the right directory
if [ ! -f "services/integration-test-suite/build.gradle" ]; then
    echo "❌ Error: Please run this script from the project root directory"
    echo "   Expected: /path/to/connector/project"
    exit 1
fi

# Build the service
echo "🔨 Building integration test suite..."
./gradlew :services:integration-test-suite:build

if [ $? -ne 0 ]; then
    echo "❌ Build failed. Please fix build errors first."
    exit 1
fi

echo "✅ Build successful!"

# Create logs directory
mkdir -p logs

echo ""
echo "📋 Test Configuration:"
echo "====================="
echo "   Environment: Sunnyvale"
echo "   IQS URL: https://insights.sunnyvale.ilabs.io"
echo "   ISS URL: https://insights-search.sunnyvale.ilabs.io"
echo "   Tenant: 39e868b6-fdfc-4118-b664-a7d4b04728e8"
echo "   Widget: 1661 (RISKY_TRAFFIC_BY_ROLES_RESOURCE_INSIGHTS)"
echo "   ISS Query: * (wildcard search)"
echo "   Port: 8096"
echo ""

# Function to test API endpoints
test_api_endpoint() {
    local url=$1
    local name=$2
    echo "🔍 Testing $name connectivity..."

    if curl -s --connect-timeout 10 "$url" > /dev/null 2>&1; then
        echo "✅ $name is reachable"
        return 0
    else
        echo "⚠️  $name connectivity test failed (this may be normal if auth is required)"
        return 1
    fi
}

# Test connectivity to Sunnyvale services
echo "🌐 Testing Sunnyvale Environment Connectivity:"
echo "=============================================="
test_api_endpoint "https://insights.sunnyvale.ilabs.io" "IQS Service"
test_api_endpoint "https://insights-search.sunnyvale.ilabs.io" "ISS Service"

echo ""
echo "🚀 Starting Integration Test Suite..."
echo "   This will test against the live Sunnyvale environment"
echo "   The service will start on port 8096"
echo ""

# Start the service with default profile (Sunnyvale environment)
echo "Starting service... (Press Ctrl+C to stop)"
echo "=========================================="
echo ""
echo "📊 Once started, you can:"
echo "   • Check status: curl http://localhost:8096/api/v1/integration-tests/status"
echo "   • Run tests manually: curl -X POST http://localhost:8096/api/v1/integration-tests/execute"
echo "   • Check pipeline health: curl http://localhost:8096/api/v1/integration-tests/pipeline-health"
echo ""

# Start the service
./gradlew :services:integration-test-suite:bootRun
